// Dynamic Content Management for AL-SALAMAT Website
// This file handles real-time updates from Firebase to the main website

class DynamicContentManager {
    constructor() {
        this.database = null;
        this.listeners = [];
        this.init();
    }

    async init() {
        try {
            // Wait for Firebase to be ready
            if (typeof firebase === 'undefined') {
                console.log('🔥 Firebase not loaded, retrying...');
                setTimeout(() => this.init(), 1000);
                return;
            }

            this.database = firebase.database();
            console.log('🚀 Dynamic Content Manager initialized');

            // Load initial content first
            await this.loadAllContent();

            // Then set up real-time listeners
            this.setupRealtimeListeners();

        } catch (error) {
            console.error('❌ Error initializing Dynamic Content Manager:', error);
        }
    }

    // Set up real-time listeners for automatic updates
    setupRealtimeListeners() {
        // Listen for company info changes
        const companyInfoRef = this.database.ref('siteContent');
        companyInfoRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                this.updateCompanyInfo(data);
            }
        });





        // Listen for contact section changes
        const contactSectionRef = this.database.ref('contactSection');
        contactSectionRef.on('value', (snapshot) => {
            const data = snapshot.val();
            console.log('Contact section data received:', data);
            if (data) {
                this.updateContactSection(data);
            }
        });

        // Listen for branches changes with enhanced logging
        const branchesRef = this.database.ref('branches');
        branchesRef.on('value', (snapshot) => {
            const data = snapshot.val();
            console.log('🔥 Firebase branches data received:', data);
            console.log('📊 Number of branches:', data ? Object.keys(data).length : 0);

            // Add a small delay to ensure DOM is ready
            setTimeout(() => {
                this.updateBranches(data);
            }, 100);
        }, (error) => {
            console.error('❌ Error listening to branches:', error);
        });

        // Listen for gallery changes
        const galleryRef = this.database.ref('gallery');
        galleryRef.on('value', (snapshot) => {
            const data = snapshot.val();
            this.updateGallery(data);
        });

        // Listen for site settings changes
        const settingsRef = this.database.ref('siteSettings');
        settingsRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                this.updateSiteSettings(data);
            }
        });

        console.log('Real-time listeners set up');
    }

    // Load all content initially
    async loadAllContent() {
        try {
            await Promise.all([
                this.loadCompanyInfo(),
                this.loadContactSection(),
                this.loadBranches(),
                this.loadGallery(),
                this.loadSiteSettings()
            ]);
            console.log('All dynamic content loaded');
        } catch (error) {
            console.error('Error loading content:', error);
        }
    }

    // Load company information
    async loadCompanyInfo() {
        try {
            const snapshot = await this.database.ref('siteContent').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateCompanyInfo(data);
            }
        } catch (error) {
            console.error('Error loading company info:', error);
        }
    }

    // Update company information in the UI
    updateCompanyInfo(data) {
        try {
            if (data && data.title) {
                const titleElement = document.getElementById('company-title');
                if (titleElement) {
                    titleElement.textContent = data.title;
                    this.animateUpdate(titleElement);
                }
            }

            if (data && data.subtitle) {
                const subtitleElement = document.getElementById('company-subtitle');
                if (subtitleElement) {
                    subtitleElement.textContent = data.subtitle;
                    this.animateUpdate(subtitleElement);
                }
            }

            if (data && data.description) {
                const descriptionElement = document.getElementById('company-description');
                if (descriptionElement) {
                    descriptionElement.textContent = data.description;
                    this.animateUpdate(descriptionElement);
                }
            }

            console.log('Company info updated');
        } catch (error) {
            console.error('Error updating company info:', error);
        }
    }





    // Load contact section
    async loadContactSection() {
        try {
            const snapshot = await this.database.ref('contactSection').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateContactSection(data);
            }
        } catch (error) {
            console.error('Error loading contact section:', error);
        }
    }

    // Update contact section in the UI
    updateContactSection(data) {
        try {
            console.log('Updating contact section with data:', data);

            if (data && data.title) {
                const titleElement = document.getElementById('contact-title');
                console.log('Title element found:', titleElement);
                if (titleElement) {
                    titleElement.textContent = data.title;
                    this.animateUpdate(titleElement);
                    console.log('Title updated to:', data.title);
                }
            }

            if (data && data.infoTitle) {
                const infoTitleElement = document.getElementById('contact-info-title');
                console.log('Info title element found:', infoTitleElement);
                if (infoTitleElement) {
                    infoTitleElement.textContent = data.infoTitle;
                    this.animateUpdate(infoTitleElement);
                    console.log('Info title updated to:', data.infoTitle);
                }
            }

            if (data && data.address) {
                const addressElement = document.getElementById('contact-address-display');
                console.log('Address element found:', addressElement);
                if (addressElement) {
                    addressElement.textContent = data.address;
                    this.animateUpdate(addressElement);
                    console.log('Address updated to:', data.address);
                }
            }

            if (data && data.hours) {
                const hoursElement = document.getElementById('contact-hours-display');
                console.log('Hours element found:', hoursElement);
                if (hoursElement) {
                    hoursElement.textContent = data.hours;
                    this.animateUpdate(hoursElement);
                    console.log('Hours updated to:', data.hours);
                }
            }

            console.log('Contact section updated successfully');
        } catch (error) {
            console.error('Error updating contact section:', error);
        }
    }

    // Load branches
    async loadBranches() {
        try {
            console.log('🔄 Loading branches from Firebase...');
            const snapshot = await this.database.ref('branches').once('value');
            const data = snapshot.val();
            console.log('📥 Branches loaded:', data);
            console.log('📊 Number of branches loaded:', data ? Object.keys(data).length : 0);

            // Force update branches
            this.updateBranches(data);
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        }
    }

    // Update branches in the UI
    updateBranches(branchesData) {
        try {
            console.log('🏢 Updating branches with data:', branchesData);
            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');

            console.log('📍 Branches grid element:', branchesGrid);
            console.log('📝 No data message element:', noDataMessage);

            if (!branchesGrid) {
                console.error('❌ Branches grid element not found!');
                return;
            }

            if (branchesData && Object.keys(branchesData).length > 0) {
                console.log('✅ Found branches data, updating UI...');

                // Hide no data message with multiple methods for reliability
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                    noDataMessage.classList.add('hidden');
                    console.log('🙈 Hidden no data message');
                }

                // Clear existing branch cards only
                const existingCards = branchesGrid.querySelectorAll('.branch-card');
                console.log(`🗑️ Removing ${existingCards.length} existing cards`);
                existingCards.forEach(card => card.remove());

                // Add branches with improved error handling
                const branchEntries = Object.entries(branchesData);
                console.log(`➕ Adding ${branchEntries.length} branches...`);

                branchEntries.forEach(([branchId, branch], index) => {
                    try {
                        console.log(`📍 Adding branch ${index + 1}:`, branch);
                        const branchCard = document.createElement('div');
                        branchCard.className = 'branch-card';
                        branchCard.setAttribute('data-branch-id', branchId);

                        // Ensure all required fields exist
                        const branchName = branch.name || 'فرع غير محدد';
                        const branchAddress = branch.address || 'عنوان غير محدد';
                        const branchPhone = branch.phone || '';

                        branchCard.innerHTML = `
                            <h3>${this.escapeHtml(branchName)}</h3>
                            <p>${this.escapeHtml(branchAddress)}</p>
                            <a href="https://maps.google.com/?q=${encodeURIComponent(branchAddress)}" target="_blank" class="location-btn">الموقع</a>
                        `;
                        branchesGrid.appendChild(branchCard);
                        console.log(`✅ Branch ${index + 1} added successfully`);
                    } catch (branchError) {
                        console.error(`❌ Error adding branch ${index + 1}:`, branchError);
                    }
                });

                // Force a reflow to ensure DOM updates
                branchesGrid.offsetHeight;

                this.animateUpdate(branchesGrid);
                console.log('🎉 Branches updated successfully!');

                // Verify the update
                const finalCards = branchesGrid.querySelectorAll('.branch-card');
                console.log(`✅ Final verification: ${finalCards.length} branch cards in DOM`);

            } else {
                console.log('⚠️ No branches data, showing no data message...');

                // Show no data message with multiple methods for reliability
                if (noDataMessage) {
                    noDataMessage.style.display = 'block';
                    noDataMessage.classList.remove('hidden');
                    console.log('👁️ Showed no data message');
                }

                // Remove existing branch cards
                const existingCards = branchesGrid.querySelectorAll('.branch-card');
                console.log(`🗑️ Removing ${existingCards.length} existing cards`);
                existingCards.forEach(card => card.remove());

                console.log('ℹ️ No branches data available');
            }
        } catch (error) {
            console.error('❌ Error updating branches:', error);
            console.error('Error details:', error.stack);
        }
    }

    // Load gallery
    async loadGallery() {
        try {
            const snapshot = await this.database.ref('gallery').once('value');
            const data = snapshot.val();
            this.updateGallery(data);
        } catch (error) {
            console.error('Error loading gallery:', error);
        }
    }

    // Update gallery in the UI
    updateGallery(galleryData) {
        try {
            const galleryGrid = document.getElementById('dynamic-gallery');
            const noDataMessage = document.getElementById('no-gallery-message');

            if (!galleryGrid) return;

            // Clear only Firebase images, keep static ones
            const firebaseImages = galleryGrid.querySelectorAll('.firebase-image');
            firebaseImages.forEach(img => img.remove());

            if (galleryData && Object.keys(galleryData).length > 0) {
                // Hide no data message
                if (noDataMessage) {
                    noDataMessage.classList.add('hidden');
                }

                Object.values(galleryData).forEach(image => {
                    const galleryItem = document.createElement('div');
                    galleryItem.className = 'gallery-item firebase-image';
                    galleryItem.innerHTML = `
                        <img src="${this.escapeHtml(image.url)}"
                             alt="${this.escapeHtml(image.alt || 'صورة من المعرض')}"
                             class="gallery-image"
                             loading="lazy">
                    `;
                    galleryGrid.appendChild(galleryItem);
                });

                this.animateUpdate(galleryGrid);
                console.log('Gallery updated');
            } else {
                // Show no data message
                if (noDataMessage) {
                    noDataMessage.classList.remove('hidden');
                }
                console.log('No gallery data available');
            }
        } catch (error) {
            console.error('Error updating gallery:', error);
        }
    }

    // Load site settings
    async loadSiteSettings() {
        try {
            const snapshot = await this.database.ref('siteSettings').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateSiteSettings(data);
            }
        } catch (error) {
            console.error('Error loading site settings:', error);
        }
    }

    // Update site settings in the UI
    updateSiteSettings(settings) {
        try {
            if (settings && settings.contactEmail) {
                const emailElement = document.getElementById('contact-email-display');
                if (emailElement) {
                    emailElement.textContent = settings.contactEmail;
                    this.animateUpdate(emailElement);
                }

                // Update email link
                const emailLink = document.querySelector('a[href^="mailto:"]');
                if (emailLink) {
                    emailLink.href = `mailto:${settings.contactEmail}`;
                }
            }

            if (settings && settings.contactPhone) {
                const phoneElement = document.getElementById('contact-phone-display');
                if (phoneElement) {
                    phoneElement.textContent = settings.contactPhone;
                    this.animateUpdate(phoneElement);
                }

                // Update phone link
                const phoneLink = document.querySelector('a[href^="tel:"]');
                if (phoneLink) {
                    phoneLink.href = `tel:${settings.contactPhone}`;
                }
            }

            console.log('Site settings updated');
        } catch (error) {
            console.error('Error updating site settings:', error);
        }
    }

    // Animate element update
    animateUpdate(element) {
        if (!element) return;

        // Show update indicator
        this.showUpdateIndicator();

        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.02)';
        element.style.boxShadow = '0 0 10px rgba(102, 126, 234, 0.3)';

        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.boxShadow = '';
        }, 300);
    }

    // Show update indicator
    showUpdateIndicator() {
        const indicator = document.getElementById('update-indicator');
        if (indicator) {
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }
    }

    // Escape HTML to prevent XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Clean up listeners
    destroy() {
        this.listeners.forEach(listener => {
            if (listener.off) {
                listener.off();
            }
        });
        this.listeners = [];
        console.log('Dynamic Content Manager destroyed');
    }
}

// Initialize when DOM is ready
// Initialize Dynamic Content Manager when DOM and Firebase are ready
function initializeDynamicContent() {
    console.log('🚀 Initializing Dynamic Content Manager...');
    try {
        if (typeof firebase !== 'undefined') {
            window.dynamicContentManager = new DynamicContentManager();
            console.log('✅ Dynamic Content Manager initialized successfully');
        } else {
            console.log('⏳ Firebase not ready yet, retrying...');
            setTimeout(initializeDynamicContent, 1000);
        }
    } catch (error) {
        console.error('❌ Error initializing Dynamic Content Manager:', error);
        // Retry after error
        setTimeout(initializeDynamicContent, 2000);
    }
}

// Start initialization when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM loaded, starting Dynamic Content Manager initialization...');
    // Give Firebase a moment to load
    setTimeout(initializeDynamicContent, 1000);
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.dynamicContentManager) {
        window.dynamicContentManager.destroy();
    }
});
