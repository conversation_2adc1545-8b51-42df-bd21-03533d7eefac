// Wait for Firebase to load, then initialize
document.addEventListener('DOMContentLoaded', function() {
    // Firebase configuration
    const firebaseConfig = {
        apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
        authDomain: "al-salamat.firebaseapp.com",
        databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
        projectId: "al-salamat",
        storageBucket: "al-salamat.firebasestorage.app",
        messagingSenderId: "108512109295",
        appId: "1:108512109295:web:84f99d95019e2101dcb11a"
    };

    // Initialize Firebase
    if (!firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
    }

    // Initialize Firebase services
    window.auth = firebase.auth();
    window.database = firebase.database();
    window.storage = firebase.storage();

    // Wait for auth state to be ready
    window.auth.onAuthStateChanged((user) => {
        if (user) {
            console.log('User authenticated:', user.email);
            // Now run the initialization
            initializeAdmin();
        } else {
            console.log('User not authenticated, redirecting to login');
            window.location.href = 'login.html';
        }
    });
});

function initializeAdmin() {
    console.log('Initializing admin panel...');
    console.log('Firebase available:', typeof firebase !== 'undefined');
    console.log('Database available:', typeof window.database !== 'undefined');
    console.log('Storage available:', typeof window.storage !== 'undefined');

    checkAdminAuth();
    loadUsers(); // Load users immediately
    setupEventListeners();
    setupBranchesEventListeners();
}

// Global variables
let currentUser = null;
let users = [];
let branches = [];

// Authentication check
function checkAdminAuth() {
    const firebaseUser = window.auth.currentUser;
    const localUser = localStorage.getItem('user');

    if (!firebaseUser || !localUser) {
        console.log('No authenticated user found');
        window.location.href = 'login.html';
        return;
    }

    currentUser = JSON.parse(localUser);

    // Special welcome message for main admin
    const welcomeText = currentUser.email === '<EMAIL>'
        ? `مرحباً، ${currentUser.displayName || 'المدير الرئيسي'} 👑`
        : `مرحباً، ${currentUser.displayName || 'المدير'}`;

    document.getElementById('admin-username').textContent = welcomeText;

    // Check if user has admin privileges
    checkAdminPrivileges();
}

// Check admin privileges
async function checkAdminPrivileges() {
    try {
        // Admin emails with full access
        const adminEmails = ['<EMAIL>']; // Main admin email

        if (!adminEmails.includes(currentUser.email)) {
            showMessage('ليس لديك صلاحيات للوصول إلى لوحة الإدارة', 'error');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }

        // Optional: Also check database for role-based permissions
        try {
            const userRef = window.database.ref(`users/${currentUser.displayName?.replace(/\s+/g, '_')}`);
            const snapshot = await userRef.once('value');
            const userData = snapshot.val();

            // If user exists in database and has admin role, allow access
            if (userData && userData.role === 'admin') {
                return;
            }
        } catch (dbError) {
            console.log('Database check failed, using email-based auth:', dbError);
        }

    } catch (error) {
        console.error('Error checking admin privileges:', error);
        showMessage('خطأ في التحقق من الصلاحيات', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Setup event listeners
function setupEventListeners() {
    // Event listeners can be added here for other sections
}

// Users management
async function loadUsers() {
    try {
        showLoading(true);

        const usersRef = window.database.ref('users');
        const snapshot = await usersRef.once('value');
        const usersData = snapshot.val();

        const usersTableBody = document.getElementById('users-table-body');
        usersTableBody.innerHTML = '';

        if (usersData) {
            users = Object.entries(usersData).map(([key, value]) => ({id: key, ...value}));

            users.forEach(user => {
                const row = document.createElement('tr');
                const roleClass = user.role === 'admin' ? 'admin' : 'user';
                const roleText = user.role === 'admin' ? 'مدير' : 'مستخدم';

                row.innerHTML = `
                    <td>${user.name || 'غير محدد'}</td>
                    <td>${user.email}</td>
                    <td>${user.phone || 'غير محدد'}</td>
                    <td><span class="user-role ${roleClass}">${roleText}</span></td>
                    <td>${formatDate(user.createdAt)}</td>
                    <td>
                        ${user.email !== '<EMAIL>' ?
                            `<button onclick="deleteUser('${user.id}')" class="admin-btn danger">حذف</button>` :
                            '<span style="color: #666;">المدير الرئيسي</span>'
                        }
                    </td>
                `;
                usersTableBody.appendChild(row);
            });
        } else {
            usersTableBody.innerHTML = '<tr><td colspan="6" class="no-data">لا توجد مستخدمين مسجلين</td></tr>';
        }

        showLoading(false);
    } catch (error) {
        console.error('Error loading users:', error);
        showMessage('خطأ في تحميل المستخدمين', 'error');
        showLoading(false);
    }
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from all menu items
    document.querySelectorAll('.admin-menu a').forEach(link => {
        link.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Add active class to clicked menu item
    event.target.classList.add('active');

    // Load section-specific data
    if (sectionId === 'users') {
        loadUsers();
    } else if (sectionId === 'branches') {
        loadBranches();
    }
}

// Delete user
async function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        try {
            await window.database.ref(`users/${userId}`).remove();
            showMessage('تم حذف المستخدم بنجاح', 'success');
            loadUsers();
        } catch (error) {
            console.error('Error deleting user:', error);
            showMessage('خطأ في حذف المستخدم', 'error');
        }
    }
}

// Utility functions
function showLoading(show) {
    document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
}

function showMessage(message, type) {
    const messageEl = document.getElementById('admin-message');
    messageEl.textContent = message;
    messageEl.className = `admin-message ${type}`;
    messageEl.style.display = 'block';

    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
}







// Logout function
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('user');
        window.location.href = 'login.html';
    }
}

// ==================== BRANCHES MANAGEMENT ====================

// Setup branches event listeners
function setupBranchesEventListeners() {
    // Add branch form submission
    const addBranchForm = document.getElementById('add-branch-form');
    if (addBranchForm) {
        addBranchForm.addEventListener('submit', handleAddBranch);
    }

    // Edit branch form submission
    const editBranchForm = document.getElementById('edit-branch-form');
    if (editBranchForm) {
        editBranchForm.addEventListener('submit', handleEditBranch);
    }

    // Close modal when clicking outside
    const editModal = document.getElementById('edit-branch-modal');
    if (editModal) {
        editModal.addEventListener('click', function(event) {
            if (event.target === editModal) {
                closeEditBranchModal();
            }
        });
    }
}

// Load branches from Firebase
async function loadBranches() {
    try {
        showLoading(true);

        const branchesRef = window.database.ref('branches');
        const snapshot = await branchesRef.once('value');
        const branchesData = snapshot.val();

        const branchesTableBody = document.getElementById('branches-table-body');
        const noBranchesData = document.getElementById('no-branches-data');

        branchesTableBody.innerHTML = '';

        if (branchesData && Object.keys(branchesData).length > 0) {
            branches = Object.entries(branchesData).map(([id, data]) => ({
                id,
                ...data
            }));

            // Sort branches by creation date (newest first)
            branches.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            branches.forEach(branch => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${escapeHtml(branch.name)}</td>
                    <td>${escapeHtml(branch.address)}</td>
                    <td>${branch.phone || 'غير محدد'}</td>
                    <td>${formatDate(branch.createdAt)}</td>
                    <td>
                        <div class="branch-actions">
                            <button class="admin-btn edit" onclick="openEditBranchModal('${branch.id}')">
                                تعديل
                            </button>
                            <button class="admin-btn danger" onclick="deleteBranch('${branch.id}')">
                                حذف
                            </button>
                        </div>
                    </td>
                `;
                branchesTableBody.appendChild(row);
            });

            noBranchesData.style.display = 'none';
        } else {
            branches = [];
            noBranchesData.style.display = 'block';
        }

        console.log('Branches loaded successfully:', branches.length);

    } catch (error) {
        console.error('Error loading branches:', error);
        showMessage('خطأ في تحميل الفروع', 'error');
    } finally {
        showLoading(false);
    }
}

// Handle add branch form submission
async function handleAddBranch(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const branchData = {
        name: formData.get('name').trim(),
        address: formData.get('address').trim(),
        phone: formData.get('phone').trim(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // Validation
    if (!branchData.name || !branchData.address) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (branchData.name.length < 2) {
        showMessage('اسم الفرع يجب أن يكون أكثر من حرفين', 'error');
        return;
    }

    if (branchData.address.length < 10) {
        showMessage('عنوان الفرع يجب أن يكون أكثر تفصيلاً', 'error');
        return;
    }

    try {
        showLoading(true);

        // Generate unique ID for the branch
        const branchId = 'branch_' + Date.now();

        // Save to Firebase
        const branchRef = window.database.ref(`branches/${branchId}`);
        await branchRef.set(branchData);

        showMessage('تم إضافة الفرع بنجاح', 'success');

        // Clear form and reload branches
        clearBranchForm();
        loadBranches();

    } catch (error) {
        console.error('Error adding branch:', error);
        showMessage('خطأ في إضافة الفرع', 'error');
    } finally {
        showLoading(false);
    }
}

// Clear branch form
function clearBranchForm() {
    document.getElementById('add-branch-form').reset();
}

// Open edit branch modal
function openEditBranchModal(branchId) {
    const branch = branches.find(b => b.id === branchId);
    if (!branch) {
        showMessage('الفرع غير موجود', 'error');
        return;
    }

    // Fill form with branch data
    document.getElementById('edit-branch-id').value = branchId;
    document.getElementById('edit-branch-name').value = branch.name;
    document.getElementById('edit-branch-address').value = branch.address;
    document.getElementById('edit-branch-phone').value = branch.phone || '';

    // Show modal
    document.getElementById('edit-branch-modal').style.display = 'flex';
}

// Close edit branch modal
function closeEditBranchModal() {
    document.getElementById('edit-branch-modal').style.display = 'none';
    document.getElementById('edit-branch-form').reset();
}

// Handle edit branch form submission
async function handleEditBranch(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const branchId = document.getElementById('edit-branch-id').value;
    const branchData = {
        name: formData.get('name').trim(),
        address: formData.get('address').trim(),
        phone: formData.get('phone').trim(),
        updatedAt: new Date().toISOString()
    };

    // Validation
    if (!branchData.name || !branchData.address) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (branchData.name.length < 2) {
        showMessage('اسم الفرع يجب أن يكون أكثر من حرفين', 'error');
        return;
    }

    if (branchData.address.length < 10) {
        showMessage('عنوان الفرع يجب أن يكون أكثر تفصيلاً', 'error');
        return;
    }

    try {
        showLoading(true);

        // Get existing branch data to preserve createdAt
        const existingBranchRef = window.database.ref(`branches/${branchId}`);
        const existingSnapshot = await existingBranchRef.once('value');
        const existingData = existingSnapshot.val();

        if (existingData) {
            branchData.createdAt = existingData.createdAt;
        }

        // Update in Firebase
        await existingBranchRef.set(branchData);

        showMessage('تم تحديث الفرع بنجاح', 'success');

        // Close modal and reload branches
        closeEditBranchModal();
        loadBranches();

    } catch (error) {
        console.error('Error updating branch:', error);
        showMessage('خطأ في تحديث الفرع', 'error');
    } finally {
        showLoading(false);
    }
}

// Delete branch
async function deleteBranch(branchId) {
    const branch = branches.find(b => b.id === branchId);
    if (!branch) {
        showMessage('الفرع غير موجود', 'error');
        return;
    }

    if (!confirm(`هل تريد حذف فرع "${branch.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    try {
        showLoading(true);

        // Delete from Firebase
        const branchRef = window.database.ref(`branches/${branchId}`);
        await branchRef.remove();

        showMessage('تم حذف الفرع بنجاح', 'success');

        // Reload branches
        loadBranches();

    } catch (error) {
        console.error('Error deleting branch:', error);
        showMessage('خطأ في حذف الفرع', 'error');
    } finally {
        showLoading(false);
    }
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}