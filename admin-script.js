// AL-SALAMAT Admin Panel Script
// Complete admin management system

class AdminPanel {
    constructor() {
        this.database = null;
        this.storage = null;
        this.auth = null;
        this.currentUser = null;
        this.isLoading = false;
        this.init();
    }

    async init() {
        try {
            console.log('🚀 Initializing Admin Panel...');
            
            // Initialize Firebase
            await this.initializeFirebase();
            
            // Check authentication
            await this.checkAuthentication();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadAllData();
            
            // Hide loading overlay
            this.hideLoading();
            
            console.log('✅ Admin Panel initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing admin panel:', error);
            this.showMessage('خطأ في تحميل لوحة الإدارة', 'error');
        }
    }

    async initializeFirebase() {
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        this.database = firebase.database();
        this.storage = firebase.storage();
        this.auth = firebase.auth();

        console.log('🔥 Firebase initialized');
    }

    async checkAuthentication() {
        return new Promise((resolve) => {
            this.auth.onAuthStateChanged(async (user) => {
                if (user) {
                    this.currentUser = user;
                    console.log('👤 User authenticated:', user.email || user.uid);

                    // Check if user has admin privileges
                    if (await this.verifyAdminAccess(user)) {
                        resolve();
                    } else {
                        console.log('❌ User does not have admin privileges');
                        alert('ليس لديك صلاحيات للوصول إلى لوحة الإدارة');
                        window.location.href = 'index.html';
                    }
                } else {
                    console.log('❌ User not authenticated');
                    // Try to authenticate with admin credentials
                    await this.tryAdminAuthentication();
                }
            });
        });
    }

    async verifyAdminAccess(user) {
        try {
            // Check if user email is admin
            const adminEmails = ['<EMAIL>'];
            if (user.email && adminEmails.includes(user.email)) {
                return true;
            }

            // Check if user has admin role in database
            if (user.uid) {
                const userSnapshot = await this.database.ref(`users/${user.uid}`).once('value');
                const userData = userSnapshot.val();
                if (userData && userData.role === 'admin') {
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Error verifying admin access:', error);
            return false;
        }
    }

    async tryAdminAuthentication() {
        try {
            console.log('🔑 Attempting admin authentication...');

            // Try to sign in with admin credentials
            const adminEmail = '<EMAIL>';
            const adminPassword = 'admin123456'; // You should change this

            const userCredential = await this.auth.signInWithEmailAndPassword(adminEmail, adminPassword);
            this.currentUser = userCredential.user;
            console.log('✅ Admin authentication successful');

        } catch (error) {
            console.error('❌ Admin authentication failed:', error);

            // If admin account doesn't exist, create it
            if (error.code === 'auth/user-not-found') {
                await this.createAdminAccount();
            } else {
                // Redirect to login page
                window.location.href = 'login.html';
            }
        }
    }

    async createAdminAccount() {
        try {
            console.log('👤 Creating admin account...');

            const adminEmail = '<EMAIL>';
            const adminPassword = 'admin123456';

            const userCredential = await this.auth.createUserWithEmailAndPassword(adminEmail, adminPassword);
            const user = userCredential.user;

            // Save admin user data
            await this.database.ref(`users/${user.uid}`).set({
                email: adminEmail,
                name: 'مدير النظام',
                role: 'admin',
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString()
            });

            this.currentUser = user;
            console.log('✅ Admin account created successfully');

        } catch (error) {
            console.error('❌ Error creating admin account:', error);
            alert('خطأ في إنشاء حساب المدير. يرجى المحاولة مرة أخرى.');
            window.location.href = 'index.html';
        }
    }

    setupEventListeners() {
        // Company Info Form
        document.getElementById('company-info-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCompanyInfo();
        });

        // Add Branch Form
        document.getElementById('add-branch-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addBranch();
        });

        // Edit Branch Form
        document.getElementById('edit-branch-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateBranch();
        });

        // Contact Form
        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveContactInfo();
        });



        // Settings Form
        document.getElementById('settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });

        console.log('📝 Event listeners setup complete');
    }

    async loadAllData() {
        try {
            await Promise.all([
                this.loadCompanyInfo(),
                this.loadBranches(),
                this.loadContactInfo(),

                this.loadSettings(),
                this.loadUsers(),
                this.loadMessages()
            ]);
            console.log('📊 All data loaded');
        } catch (error) {
            console.error('❌ Error loading data:', error);
        }
    }

    // Company Info Management
    async loadCompanyInfo() {
        try {
            const snapshot = await this.database.ref('siteContent').once('value');
            const data = snapshot.val();
            
            if (data) {
                document.getElementById('company-title').value = data.title || '';
                document.getElementById('company-subtitle').value = data.subtitle || '';
                document.getElementById('company-description').value = data.description || '';
            }
        } catch (error) {
            console.error('❌ Error loading company info:', error);
        }
    }

    async saveCompanyInfo() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('company-info-form'));
            const data = {
                title: formData.get('title'),
                subtitle: formData.get('subtitle'),
                description: formData.get('description'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('siteContent').set(data);
            this.showMessage('تم حفظ معلومات الشركة بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error saving company info:', error);
            this.showMessage('خطأ في حفظ معلومات الشركة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Branches Management
    async loadBranches() {
        try {
            const snapshot = await this.database.ref('branches').once('value');
            const data = snapshot.val();
            const container = document.getElementById('branches-grid');
            const noData = document.getElementById('no-branches');
            
            container.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                noData.style.display = 'none';
                
                Object.entries(data).forEach(([id, branch]) => {
                    const branchElement = this.createBranchElement(id, branch);
                    container.appendChild(branchElement);
                });
            } else {
                noData.style.display = 'block';
            }
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        }
    }

    createBranchElement(id, branch) {
        const div = document.createElement('div');
        div.className = 'branch-item';
        div.innerHTML = `
            <h4>${this.escapeHtml(branch.name)}</h4>
            <p><strong>العنوان:</strong> ${this.escapeHtml(branch.address)}</p>
            <p><strong>الهاتف:</strong> ${this.escapeHtml(branch.phone || 'غير محدد')}</p>
            <div class="branch-actions">
                <button class="admin-btn primary" onclick="adminPanel.editBranch('${id}')">✏️ تعديل</button>
                <button class="admin-btn danger" onclick="adminPanel.deleteBranch('${id}')">🗑️ حذف</button>
            </div>
        `;
        return div;
    }

    async addBranch() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('add-branch-form'));
            const branchData = {
                name: formData.get('name'),
                address: formData.get('address'),
                phone: formData.get('phone'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Validation
            if (!branchData.name || !branchData.address) {
                this.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const branchId = 'branch_' + Date.now();
            await this.database.ref(`branches/${branchId}`).set(branchData);
            
            this.showMessage('تم إضافة الفرع بنجاح', 'success');
            document.getElementById('add-branch-form').reset();
            await this.loadBranches();
            
        } catch (error) {
            console.error('❌ Error adding branch:', error);
            this.showMessage('خطأ في إضافة الفرع', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async editBranch(branchId) {
        try {
            const snapshot = await this.database.ref(`branches/${branchId}`).once('value');
            const branch = snapshot.val();
            
            if (branch) {
                document.getElementById('edit-branch-id').value = branchId;
                document.getElementById('edit-branch-name').value = branch.name;
                document.getElementById('edit-branch-address').value = branch.address;
                document.getElementById('edit-branch-phone').value = branch.phone || '';
                
                document.getElementById('edit-branch-modal').classList.add('active');
            }
        } catch (error) {
            console.error('❌ Error loading branch for edit:', error);
            this.showMessage('خطأ في تحميل بيانات الفرع', 'error');
        }
    }

    async updateBranch() {
        try {
            this.showLoading();
            
            const branchId = document.getElementById('edit-branch-id').value;
            const formData = new FormData(document.getElementById('edit-branch-form'));
            
            const branchData = {
                name: formData.get('name'),
                address: formData.get('address'),
                phone: formData.get('phone'),
                updatedAt: new Date().toISOString()
            };

            // Get existing data to preserve createdAt
            const snapshot = await this.database.ref(`branches/${branchId}`).once('value');
            const existingData = snapshot.val();
            if (existingData && existingData.createdAt) {
                branchData.createdAt = existingData.createdAt;
            }

            await this.database.ref(`branches/${branchId}`).set(branchData);
            
            this.showMessage('تم تحديث الفرع بنجاح', 'success');
            this.closeEditModal();
            await this.loadBranches();
            
        } catch (error) {
            console.error('❌ Error updating branch:', error);
            this.showMessage('خطأ في تحديث الفرع', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async deleteBranch(branchId) {
        if (!confirm('هل أنت متأكد من حذف هذا الفرع؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`branches/${branchId}`).remove();
            this.showMessage('تم حذف الفرع بنجاح', 'success');
            await this.loadBranches();
        } catch (error) {
            console.error('❌ Error deleting branch:', error);
            this.showMessage('خطأ في حذف الفرع', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Contact Info Management
    async loadContactInfo() {
        try {
            const snapshot = await this.database.ref('contactSection').once('value');
            const data = snapshot.val();
            
            if (data) {
                document.getElementById('contact-title').value = data.title || '';
                document.getElementById('contact-info-title').value = data.infoTitle || '';
                document.getElementById('contact-address').value = data.address || '';
                document.getElementById('contact-hours').value = data.hours || '';
            }
        } catch (error) {
            console.error('❌ Error loading contact info:', error);
        }
    }

    async saveContactInfo() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('contact-form'));
            const data = {
                title: formData.get('title'),
                infoTitle: formData.get('infoTitle'),
                address: formData.get('address'),
                hours: formData.get('hours'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('contactSection').set(data);
            this.showMessage('تم حفظ معلومات التواصل بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error saving contact info:', error);
            this.showMessage('خطأ في حفظ معلومات التواصل', 'error');
        } finally {
            this.hideLoading();
        }
    }



    // Settings Management
    async loadSettings() {
        try {
            const snapshot = await this.database.ref('siteSettings').once('value');
            const data = snapshot.val();
            
            if (data) {
                document.getElementById('site-email').value = data.contactEmail || '';
                document.getElementById('site-phone').value = data.contactPhone || '';
            }
        } catch (error) {
            console.error('❌ Error loading settings:', error);
        }
    }

    async saveSettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('settings-form'));
            const data = {
                contactEmail: formData.get('contactEmail'),
                contactPhone: formData.get('contactPhone'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('siteSettings').set(data);
            this.showMessage('تم حفظ الإعدادات بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error saving settings:', error);
            this.showMessage('خطأ في حفظ الإعدادات', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Users Management
    async loadUsers() {
        try {
            const snapshot = await this.database.ref('users').once('value');
            const data = snapshot.val();
            const tbody = document.getElementById('users-table');
            
            tbody.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                Object.entries(data).forEach(([id, user]) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${this.escapeHtml(user.name || 'غير محدد')}</td>
                        <td>${this.escapeHtml(user.email)}</td>
                        <td>${this.escapeHtml(user.phone || 'غير محدد')}</td>
                        <td><span class="badge ${user.role === 'admin' ? 'admin' : 'user'}">${user.role === 'admin' ? 'مدير' : 'مستخدم'}</span></td>
                        <td>${this.formatDate(user.createdAt)}</td>
                        <td>
                            ${user.email !== '<EMAIL>' ? 
                                `<button class="admin-btn danger" onclick="adminPanel.deleteUser('${id}')">حذف</button>` : 
                                '<span style="color: #666;">المدير الرئيسي</span>'
                            }
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="6" class="no-data">لا توجد مستخدمين مسجلين</td></tr>';
            }
        } catch (error) {
            console.error('❌ Error loading users:', error);
        }
    }

    async deleteUser(userId) {
        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`users/${userId}`).remove();
            this.showMessage('تم حذف المستخدم بنجاح', 'success');
            await this.loadUsers();
        } catch (error) {
            console.error('❌ Error deleting user:', error);
            this.showMessage('خطأ في حذف المستخدم', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Messages Management
    async loadMessages() {
        try {
            const snapshot = await this.database.ref('contactForms').once('value');
            const data = snapshot.val();
            const container = document.getElementById('messages-container');
            
            container.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                const messages = Object.entries(data).sort((a, b) => 
                    new Date(b[1].submittedAt) - new Date(a[1].submittedAt)
                );
                
                messages.forEach(([id, message]) => {
                    const messageElement = this.createMessageElement(id, message);
                    container.appendChild(messageElement);
                });
            } else {
                container.innerHTML = '<div class="no-data"><p>لا توجد رسائل واردة</p></div>';
            }
        } catch (error) {
            console.error('❌ Error loading messages:', error);
        }
    }

    createMessageElement(id, message) {
        const div = document.createElement('div');
        div.className = 'message-item';
        div.innerHTML = `
            <div class="message-header">
                <span class="message-sender">${this.escapeHtml(message.name)} - ${this.escapeHtml(message.email)}</span>
                <span class="message-date">${this.formatDate(message.submittedAt)}</span>
            </div>
            <p><strong>الهاتف:</strong> ${this.escapeHtml(message.phone)}</p>
            <div class="message-content">${this.escapeHtml(message.message)}</div>
            <div class="branch-actions" style="margin-top: 1rem;">
                <button class="admin-btn danger" onclick="adminPanel.deleteMessage('${id}')">🗑️ حذف</button>
            </div>
        `;
        return div;
    }

    async deleteMessage(messageId) {
        if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`contactForms/${messageId}`).remove();
            this.showMessage('تم حذف الرسالة بنجاح', 'success');
            await this.loadMessages();
        } catch (error) {
            console.error('❌ Error deleting message:', error);
            this.showMessage('خطأ في حذف الرسالة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Utility Functions
    showLoading() {
        this.isLoading = true;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    hideLoading() {
        this.isLoading = false;
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    showMessage(message, type) {
        const messageEl = document.getElementById('admin-message');
        messageEl.textContent = message;
        messageEl.className = `admin-message ${type}`;
        messageEl.style.display = 'block';

        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        if (!dateString) return 'غير محدد';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
    }

    closeEditModal() {
        document.getElementById('edit-branch-modal').classList.remove('active');
    }
}

// Global Functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from all menu items
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Add active class to clicked menu item
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');
}

function previewSite() {
    window.open('index.html', '_blank');
}

function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        firebase.auth().signOut().then(() => {
            localStorage.removeItem('user');
            window.location.href = 'login.html';
        });
    }
}

function closeEditModal() {
    document.getElementById('edit-branch-modal').classList.remove('active');
}

// Initialize Admin Panel
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
});
