<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - AL-SALAMAT</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <nav class="admin-nav">
            <div class="admin-logo">AL-SALAMAT - لوحة الإدارة</div>
            <div class="admin-user-info">
                <span id="admin-username">مرحباً، المدير</span>
                <button onclick="logout()" class="logout-btn">تسجيل الخروج</button>
            </div>
        </nav>
    </header>

    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <ul class="admin-menu">
            <li><a href="#users" onclick="showSection('users')" class="active">إدارة المستخدمين</a></li>
            <li><a href="#branches" onclick="showSection('branches')">إدارة الفروع</a></li>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Users Management Section -->
        <section id="users" class="admin-section active">
            <h2>إدارة المستخدمين</h2>

            <div class="users-table-container">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>رقم الهاتف</th>
                            <th>الدور</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Branches Management Section -->
        <section id="branches" class="admin-section">
            <h2>إدارة الفروع</h2>

            <!-- Add Branch Form -->
            <div class="form-container">
                <h3>إضافة فرع جديد</h3>
                <form id="add-branch-form">
                    <div class="form-group">
                        <label for="branch-name">اسم الفرع *</label>
                        <input type="text" id="branch-name" name="name" required placeholder="مثال: فرع الرياض">
                    </div>

                    <div class="form-group">
                        <label for="branch-address">عنوان الفرع *</label>
                        <textarea id="branch-address" name="address" required placeholder="العنوان الكامل للفرع"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="branch-phone">رقم الهاتف</label>
                        <input type="tel" id="branch-phone" name="phone" placeholder="مثال: +966501234567">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="admin-btn">إضافة الفرع</button>
                        <button type="button" class="admin-btn secondary" onclick="clearBranchForm()">مسح النموذج</button>
                    </div>
                </form>
            </div>

            <!-- Branches List -->
            <div class="branches-list-container">
                <h3>قائمة الفروع</h3>
                <div class="branches-table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>اسم الفرع</th>
                                <th>العنوان</th>
                                <th>رقم الهاتف</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="branches-table-body">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div id="no-branches-data" class="no-data" style="display: none;">
                    <p>لا توجد فروع مضافة حالياً</p>
                </div>
            </div>
        </section>

    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Success/Error Messages -->
    <div id="admin-message" class="admin-message" style="display: none;"></div>

    <!-- Edit Branch Modal -->
    <div id="edit-branch-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل الفرع</h3>
                <span class="close" onclick="closeEditBranchModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="edit-branch-form">
                    <input type="hidden" id="edit-branch-id">

                    <div class="form-group">
                        <label for="edit-branch-name">اسم الفرع *</label>
                        <input type="text" id="edit-branch-name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit-branch-address">عنوان الفرع *</label>
                        <textarea id="edit-branch-address" name="address" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="edit-branch-phone">رقم الهاتف</label>
                        <input type="tel" id="edit-branch-phone" name="phone">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="admin-btn">حفظ التغييرات</button>
                        <button type="button" class="admin-btn secondary" onclick="closeEditBranchModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
