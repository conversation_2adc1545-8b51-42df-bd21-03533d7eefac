<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة AL-SALAMAT</title>
    <link rel="stylesheet" href="admin-styles.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <p>جاري التحميل...</p>
    </div>

    <!-- Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <h1>🛠️ لوحة إدارة AL-SALAMAT</h1>
            <div class="admin-header-actions">
                <div class="connection-status" id="admin-connection-status">
                    <span class="status-indicator online"></span>
                    <span>متصل</span>
                </div>
                <button class="admin-btn secondary" onclick="previewSite()">👁️ معاينة الموقع</button>
                <button class="admin-btn danger" onclick="logout()">🚪 تسجيل الخروج</button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="admin-sidebar">
            <div class="admin-menu">
                <button class="menu-item active" onclick="showSection('company-info')" data-section="company-info">
                    🏢 معلومات الشركة
                </button>
                <button class="menu-item" onclick="showSection('branches')" data-section="branches">
                    🏪 إدارة الفروع
                </button>
                <button class="menu-item" onclick="showSection('contact')" data-section="contact">
                    📞 معلومات التواصل
                </button>

                <button class="menu-item" onclick="showSection('settings')" data-section="settings">
                    ⚙️ إعدادات الموقع
                </button>
                <button class="menu-item" onclick="showSection('users')" data-section="users">
                    👥 إدارة المستخدمين
                </button>
                <button class="menu-item" onclick="showSection('messages')" data-section="messages">
                    📧 الرسائل الواردة
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Success/Error Messages -->
            <div class="admin-message" id="admin-message"></div>

            <!-- Company Info Section -->
            <section class="admin-section active" id="company-info">
                <div class="section-header">
                    <h2>🏢 معلومات الشركة</h2>
                    <p>تحكم في العنوان الرئيسي والفرعي ووصف الشركة</p>
                </div>
                
                <div class="admin-card">
                    <form id="company-info-form" class="admin-form">
                        <div class="form-group">
                            <label for="company-title">العنوان الرئيسي</label>
                            <input type="text" id="company-title" name="title" placeholder="AL-SALAMAT" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="company-subtitle">العنوان الفرعي</label>
                            <input type="text" id="company-subtitle" name="subtitle" placeholder="رائدة في زجاج السيارات" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="company-description">وصف الشركة</label>
                            <textarea id="company-description" name="description" rows="4" placeholder="وصف مختصر عن الشركة وخدماتها"></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadCompanyInfo()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Branches Section -->
            <section class="admin-section" id="branches">
                <div class="section-header">
                    <h2>🏪 إدارة الفروع</h2>
                    <p>إضافة وتعديل وحذف فروع الشركة</p>
                </div>
                
                <!-- Add New Branch -->
                <div class="admin-card">
                    <h3>➕ إضافة فرع جديد</h3>
                    <form id="add-branch-form" class="admin-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="branch-name">اسم الفرع</label>
                                <input type="text" id="branch-name" name="name" placeholder="فرع الرياض" required>
                            </div>
                            <div class="form-group">
                                <label for="branch-phone">رقم الهاتف</label>
                                <input type="tel" id="branch-phone" name="phone" placeholder="+966501234567">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="branch-address">العنوان الكامل</label>
                            <textarea id="branch-address" name="address" rows="3" placeholder="العنوان التفصيلي للفرع" required></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">➕ إضافة الفرع</button>
                            <button type="reset" class="admin-btn secondary">🗑️ مسح النموذج</button>
                        </div>
                    </form>
                </div>
                
                <!-- Existing Branches -->
                <div class="admin-card">
                    <h3>📋 الفروع الحالية</h3>
                    <div class="branches-grid" id="branches-grid">
                        <div class="no-data" id="no-branches">
                            <p>لا توجد فروع مضافة حالياً</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Section -->
            <section class="admin-section" id="contact">
                <div class="section-header">
                    <h2>📞 معلومات التواصل</h2>
                    <p>تحديث معلومات التواصل والعنوان وساعات العمل</p>
                </div>
                
                <div class="admin-card">
                    <form id="contact-form" class="admin-form">
                        <div class="form-group">
                            <label for="contact-title">عنوان قسم التواصل</label>
                            <input type="text" id="contact-title" name="title" placeholder="اتصل بنا" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-info-title">عنوان معلومات التواصل</label>
                            <input type="text" id="contact-info-title" name="infoTitle" placeholder="معلومات التواصل" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-address">العنوان الفيزيائي</label>
                            <textarea id="contact-address" name="address" rows="3" placeholder="العنوان الكامل للشركة" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-hours">ساعات العمل</label>
                            <input type="text" id="contact-hours" name="hours" placeholder="السبت - الخميس: 8:00 ص - 10:00 م" required>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadContactInfo()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>



            <!-- Settings Section -->
            <section class="admin-section" id="settings">
                <div class="section-header">
                    <h2>⚙️ إعدادات الموقع</h2>
                    <p>إعدادات عامة للموقع ومعلومات التواصل الأساسية</p>
                </div>
                
                <div class="admin-card">
                    <form id="settings-form" class="admin-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="site-email">البريد الإلكتروني</label>
                                <input type="email" id="site-email" name="contactEmail" placeholder="<EMAIL>" required>
                            </div>
                            <div class="form-group">
                                <label for="site-phone">رقم الهاتف</label>
                                <input type="tel" id="site-phone" name="contactPhone" placeholder="+966501234567" required>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ الإعدادات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadSettings()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Users Section -->
            <section class="admin-section" id="users">
                <div class="section-header">
                    <h2>👥 إدارة المستخدمين</h2>
                    <p>عرض وإدارة المستخدمين المسجلين</p>
                </div>
                
                <div class="admin-card">
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الدور</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="users-table">
                                <tr>
                                    <td colspan="6" class="no-data">جاري تحميل المستخدمين...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="admin-section" id="messages">
                <div class="section-header">
                    <h2>📧 الرسائل الواردة</h2>
                    <p>عرض وإدارة رسائل التواصل من العملاء</p>
                </div>
                
                <div class="admin-card">
                    <div class="messages-container" id="messages-container">
                        <div class="no-data">
                            <p>جاري تحميل الرسائل...</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Edit Branch Modal -->
    <div class="modal" id="edit-branch-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل الفرع</h3>
                <button class="modal-close" onclick="closeEditModal()">&times;</button>
            </div>
            <form id="edit-branch-form" class="admin-form">
                <input type="hidden" id="edit-branch-id">
                
                <div class="form-group">
                    <label for="edit-branch-name">اسم الفرع</label>
                    <input type="text" id="edit-branch-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="edit-branch-phone">رقم الهاتف</label>
                    <input type="tel" id="edit-branch-phone" name="phone">
                </div>
                
                <div class="form-group">
                    <label for="edit-branch-address">العنوان</label>
                    <textarea id="edit-branch-address" name="address" rows="3" required></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                    <button type="button" class="admin-btn secondary" onclick="closeEditModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="admin-script.js"></script>
</body>
</html>
