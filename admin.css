/* Admin Panel Styles - Users Management */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    background: #f5f7fa;
    direction: rtl;
}

/* Admin Header */
.admin-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.admin-logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 250px;
    height: calc(100vh - 70px);
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.admin-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-menu li {
    border-bottom: 1px solid #eee;
}

.admin-menu a {
    display: block;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-menu a:hover,
.admin-menu a.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

/* Main Content */
.admin-main {
    margin-top: 70px;
    margin-right: 250px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
}

.admin-section {
    display: none;
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.admin-section.active {
    display: block;
}

.admin-section h2 {
    color: #667eea;
    margin-bottom: 2rem;
    font-size: 2rem;
}

/* Users Table */
.users-table-container {
    overflow-x: auto;
    margin-top: 1rem;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.admin-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

.admin-table td {
    color: #333;
    font-size: 0.95rem;
}

/* User Role Badge */
.user-role {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
}

.user-role.admin {
    background: #28a745;
    color: white;
}

.user-role.user {
    background: #6c757d;
    color: white;
}

/* Action Buttons */
.admin-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-left: 0.25rem;
}

.admin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.admin-btn.danger {
    background: #dc3545;
}

.admin-btn.danger:hover {
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 4000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Messages */
.admin-message {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 1rem 2rem;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 3000;
    transition: all 0.3s ease;
}

.admin-message.success {
    background: #28a745;
}

.admin-message.error {
    background: #dc3545;
}

/* No Data Message */
.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }

    .admin-main {
        margin-right: 0;
        padding: 1rem;
    }

    .admin-nav {
        padding: 1rem;
    }

    .admin-logo {
        font-size: 1.2rem;
    }

    .admin-table {
        font-size: 0.9rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 0.5rem;
    }

    .admin-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}



.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    min-height: 120px;
    line-height: 1.6;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.admin-btn.secondary {
    background: #6c757d;
}

.admin-btn.secondary:hover {
    background: #5a6268;
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}



.preview-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.preview-content h4 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    text-align: center;
}

.preview-content p {
    color: #333;
    line-height: 1.8;
    font-size: 1rem;
    text-align: justify;
    margin: 0;
}



/* Character Counter */
.char-counter {
    font-size: 0.85rem;
    color: #666;
    text-align: left;
    margin-top: 0.25rem;
}

.char-counter.warning {
    color: #ffc107;
}

.char-counter.danger {
    color: #dc3545;
}

/* Branches Management Styles */
.form-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
}

.form-container h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.branches-list-container {
    margin-top: 2rem;
}

.branches-list-container h3 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.branches-table-container {
    margin-top: 1rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 5000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 10px 10px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 2rem;
}

/* Branch Card Styles for Table */
.branch-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.admin-btn.edit {
    background: #28a745;
}

.admin-btn.edit:hover {
    background: #218838;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* Responsive Modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header {
        padding: 1rem 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .branch-actions {
        flex-direction: column;
    }

    .admin-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
